"""
Azure Functions App - AutoDefectTriage
Main function app configuration for Azure Functions v2 programming model.
"""

import azure.functions as func
import logging

# Create the function app
app = func.FunctionApp()

# Configure logging
logging.basicConfig(level=logging.INFO)


@app.function_name(name="workitem_created")
@app.route(route="workitem_created", auth_level=func.AuthLevel.FUNCTION)
async def workitem_created_trigger(req: func.HttpRequest) -> func.HttpResponse:
    """
    HTTP trigger for processing Bug/defect work items from the last 24 hours.

    This function queries Azure DevOps for all Bug work items created in the last 24 hours
    and processes them through the AI triage pipeline for testing purposes.

    Query parameters:
    - hours_back: Number of hours to look back (default: 24)
    - dry_run: If true, shows what would be processed without making changes (default: false)
    """
    try:
        # Import the workitem processing logic lazily to avoid startup issues
        from __app__.workitem_created.handler import process_workitem_webhook
        return await process_workitem_webhook(req)
    except Exception as e:
        logging.error(f"Error in workitem_created_trigger: {str(e)}")
        return func.HttpResponse(
            f"Error processing request: {str(e)}",
            status_code=500
        )


@app.function_name(name="step_by_step_workflow")
@app.route(route="step_by_step_workflow", auth_level=func.AuthLevel.FUNCTION)
async def step_by_step_workflow_trigger(req: func.HttpRequest) -> func.HttpResponse:
    """
    HTTP trigger for executing the complete step-by-step defect triage workflow.

    This function orchestrates the complete process:
    1. Work Item Creation/Retrieval
    2. Historical Analysis & Pattern Recognition
    3. AI-Powered Notification Message Generation
    4. Email Notification Delivery
    5. Teams Message Broadcasting

    Supports both single work item and batch processing modes.
    """
    from .__app__.step_by_step_workflow import main as workflow_main
    return await workflow_main(req)


@app.function_name(name="backfill_job")
@app.timer_trigger(schedule="0 0 2 * * *", arg_name="timer", run_on_startup=False)
async def backfill_job_trigger(timer: func.TimerRequest) -> None:
    """
    Timer trigger for backfill job that runs daily at 2 AM.

    This function processes historical work items that may have been missed
    or need reprocessing through the triage pipeline.
    """
    try:
        # Import the backfill job logic lazily to avoid startup issues
        from __app__.backfill_job import main as backfill_job_main
        await backfill_job_main(timer)
    except Exception as e:
        logging.error(f"Error in backfill_job_trigger: {str(e)}")
        raise


@app.function_name(name="team_notification")
@app.timer_trigger(schedule="0 0 9 * * *", arg_name="timer", run_on_startup=False)
async def team_notification_trigger(timer: func.TimerRequest) -> None:
    """
    Timer trigger for team notifications that runs daily at 9 AM UTC.

    This function analyzes latest work items and sends notifications to the team.
    """
    try:
        # Import the team notification logic lazily to avoid startup issues
        from __app__.team_notification_function import main as team_notification_main
        await team_notification_main(timer)
    except Exception as e:
        logging.error(f"Error in team_notification_trigger: {str(e)}")
        raise


@app.function_name(name="critical_notification")
@app.route(route="critical_notification", auth_level=func.AuthLevel.FUNCTION)
async def critical_notification_trigger(req: func.HttpRequest) -> func.HttpResponse:
    """
    HTTP trigger for critical work item notifications.

    This function receives work item data and processes it for immediate notifications
    if it meets critical criteria.
    """
    try:
        from __app__.notification_functions import process_critical_notification
        return await process_critical_notification(req)
    except Exception as e:
        logging.error(f"Error in critical_notification_trigger: {str(e)}")
        return func.HttpResponse(
            f"Error processing critical notification: {str(e)}",
            status_code=500
        )


@app.function_name(name="work_item_check")
@app.timer_trigger(schedule="0 0 */4 * * *", arg_name="timer", run_on_startup=False)
async def work_item_check_trigger(timer: func.TimerRequest) -> None:
    """
    Timer trigger for checking new/updated work items every 4 hours.

    This function checks for work items created or updated in the last 24 hours
    and processes them for notifications.
    """
    try:
        from __app__.notification_functions import check_new_updated_work_items
        await check_new_updated_work_items(timer)
    except Exception as e:
        logging.error(f"Error in work_item_check_trigger: {str(e)}")
        raise


@app.function_name(name="aging_reminders")
@app.timer_trigger(schedule="0 0 */4 * * *", arg_name="timer", run_on_startup=False)
async def aging_reminders_trigger(timer: func.TimerRequest) -> None:
    """
    Timer trigger for aging work item reminders that runs every 4 hours.

    This function checks for aging work items and sends reminder notifications.
    """
    try:
        from __app__.notification_functions import process_aging_reminders
        await process_aging_reminders(timer)
    except Exception as e:
        logging.error(f"Error in aging_reminders_trigger: {str(e)}")
        raise


@app.function_name(name="work_item_check_manual")
@app.route(route="work_item_check", auth_level=func.AuthLevel.ANONYMOUS)
async def work_item_check_manual_trigger(req: func.HttpRequest) -> func.HttpResponse:
    """
    HTTP trigger for manually checking work items (for testing).

    This function provides the same functionality as the timer trigger but can be
    called manually via HTTP for testing purposes.
    """
    try:
        from __app__.notification_functions import manual_work_item_check
        return await manual_work_item_check(req)
    except Exception as e:
        logging.error(f"Error in work_item_check_manual_trigger: {str(e)}")
        return func.HttpResponse(
            f"Error processing work item check: {str(e)}",
            status_code=500
        )


@app.function_name(name="notification_webhook")
@app.route(route="notification_webhook", auth_level=func.AuthLevel.FUNCTION)
async def notification_webhook_trigger(req: func.HttpRequest) -> func.HttpResponse:
    """
    HTTP trigger for notification status webhooks.

    This function handles callbacks from notification services to track delivery status
    and user actions.
    """
    try:
        from __app__.notification_functions import handle_notification_webhook
        return await handle_notification_webhook(req)
    except Exception as e:
        logging.error(f"Error in notification_webhook_trigger: {str(e)}")
        return func.HttpResponse(
            f"Error processing notification webhook: {str(e)}",
            status_code=500
        )


@app.function_name(name="teams_response")
@app.route(route="teams_response", auth_level=func.AuthLevel.FUNCTION)
async def teams_response_trigger(req: func.HttpRequest) -> func.HttpResponse:
    """
    HTTP trigger for Teams adaptive card responses.

    This function handles responses from Teams adaptive cards including
    replyText and priority updates from users.

    Expected payload:
    {
        "work_item_id": 12345,
        "replyText": "User response text",
        "priority": 2,
        "user_email": "<EMAIL>",
        "user_name": "User Name"
    }
    """
    try:
        from __app__.teams_response_handler import teams_response_webhook
        return await teams_response_webhook(req)
    except Exception as e:
        logging.error(f"Error in teams_response_trigger: {str(e)}")
        return func.HttpResponse(
            f"Error processing Teams response: {str(e)}",
            status_code=500
        )


@app.function_name(name="logic_app_response")
@app.route(route="logic_app_response", auth_level=func.AuthLevel.FUNCTION)
async def logic_app_response_trigger(req: func.HttpRequest) -> func.HttpResponse:
    """
    HTTP trigger for Logic App responses.

    This function handles responses from Logic Apps with the specific format:
    {
        "replyText": "@{body('Post_adaptive_card_and_wait_for_a_response')?['data']?['replyText']}",
        "priority": "@{body('Post_adaptive_card_and_wait_for_a_response')?['data']?['priority']}"
    }

    Work item ID should be provided as query parameter: ?work_item_id=12345
    """
    try:
        from __app__.teams_response_handler import process_logic_app_response
        return await process_logic_app_response(req)
    except Exception as e:
        logging.error(f"Error in logic_app_response_trigger: {str(e)}")
        return func.HttpResponse(
            f"Error processing Logic App response: {str(e)}",
            status_code=500
        )
