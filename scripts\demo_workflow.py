#!/usr/bin/env python3
"""
AutoDefectTriage Step-by-Step Workflow Demonstration
===================================================

This script demonstrates how to use the step-by-step workflow system.
It shows both single work item processing and batch processing modes.

Usage:
    python demo_workflow.py --single 12345
    python demo_workflow.py --batch 24
    python demo_workflow.py --demo
"""

import asyncio
import json
import argparse
import aiohttp
from datetime import datetime
from typing import Dict, Any


class WorkflowDemo:
    """Demonstration client for the step-by-step workflow."""
    
    def __init__(self, function_url: str, function_key: str = None):
        """
        Initialize the demo client.
        
        Args:
            function_url: Base URL of the Azure Function app
            function_key: Function key for authentication (if required)
        """
        self.function_url = function_url.rstrip('/')
        self.function_key = function_key
        self.workflow_endpoint = f"{self.function_url}/api/step_by_step_workflow"
    
    async def execute_single_workflow(self, work_item_id: str) -> Dict[str, Any]:
        """
        Execute workflow for a single work item.
        
        Args:
            work_item_id: ID of the work item to process
            
        Returns:
            Workflow execution results
        """
        print(f"\n🚀 Starting workflow for work item: {work_item_id}")
        print("=" * 60)
        
        payload = {"work_item_id": work_item_id}
        
        async with aiohttp.ClientSession() as session:
            headers = {"Content-Type": "application/json"}
            if self.function_key:
                headers["x-functions-key"] = self.function_key
            
            print(f"📤 Sending request to: {self.workflow_endpoint}")
            print(f"📋 Payload: {json.dumps(payload, indent=2)}")
            
            start_time = datetime.utcnow()
            
            async with session.post(
                self.workflow_endpoint,
                json=payload,
                headers=headers
            ) as response:
                
                end_time = datetime.utcnow()
                execution_time = (end_time - start_time).total_seconds()
                
                result = await response.json()
                
                print(f"\n📥 Response received (HTTP {response.status})")
                print(f"⏱️  Request time: {execution_time:.2f} seconds")
                
                self._print_workflow_results(result)
                
                return result
    
    async def execute_batch_workflow(self, hours_back: int) -> Dict[str, Any]:
        """
        Execute workflow for work items from the last N hours.
        
        Args:
            hours_back: Number of hours to look back
            
        Returns:
            Batch processing results
        """
        print(f"\n🚀 Starting batch workflow for last {hours_back} hours")
        print("=" * 60)
        
        payload = {"hours_back": hours_back}
        
        async with aiohttp.ClientSession() as session:
            headers = {"Content-Type": "application/json"}
            if self.function_key:
                headers["x-functions-key"] = self.function_key
            
            print(f"📤 Sending request to: {self.workflow_endpoint}")
            print(f"📋 Payload: {json.dumps(payload, indent=2)}")
            
            start_time = datetime.utcnow()
            
            async with session.post(
                self.workflow_endpoint,
                json=payload,
                headers=headers
            ) as response:
                
                end_time = datetime.utcnow()
                execution_time = (end_time - start_time).total_seconds()
                
                result = await response.json()
                
                print(f"\n📥 Response received (HTTP {response.status})")
                print(f"⏱️  Request time: {execution_time:.2f} seconds")
                
                self._print_batch_results(result)
                
                return result
    
    def _print_workflow_results(self, result: Dict[str, Any]):
        """Print formatted workflow results for single work item."""
        print("\n📊 WORKFLOW RESULTS")
        print("-" * 40)
        
        # Basic info
        print(f"Work Item ID: {result.get('work_item_id', 'N/A')}")
        print(f"Success: {'✅' if result.get('success') else '❌'}")
        print(f"Processing Mode: {result.get('processing_mode', 'N/A')}")
        print(f"Total Execution Time: {result.get('total_time', 0):.2f}s")
        
        # Step timings
        step_timings = result.get('step_timings', {})
        if step_timings:
            print(f"\n⏱️  STEP TIMINGS")
            print("-" * 20)
            for step, timing in step_timings.items():
                step_name = step.replace('_', ' ').title()
                print(f"  {step_name}: {timing:.2f}s")
        
        # Notifications
        print(f"\n📧 NOTIFICATIONS")
        print("-" * 20)
        print(f"  Email Sent: {'✅' if result.get('email_sent') else '❌'}")
        print(f"  Teams Sent: {'✅' if result.get('teams_sent') else '❌'}")
        
        # Triage results
        triage_result = result.get('triage_result')
        if triage_result:
            print(f"\n🤖 AI TRIAGE RESULTS")
            print("-" * 20)
            print(f"  Assigned To: {triage_result.get('assigned_to', 'N/A')}")
            print(f"  Priority: {triage_result.get('priority', 'N/A')}")
            print(f"  Confidence: {triage_result.get('confidence_score', 0):.2f}")
            print(f"  Duplicates Found: {len(triage_result.get('duplicates', []))}")
        
        # Errors
        errors = result.get('errors', [])
        if errors:
            print(f"\n❌ ERRORS ({len(errors)})")
            print("-" * 20)
            for i, error in enumerate(errors, 1):
                print(f"  {i}. {error}")
    
    def _print_batch_results(self, result: Dict[str, Any]):
        """Print formatted results for batch processing."""
        print("\n📊 BATCH PROCESSING RESULTS")
        print("-" * 40)
        
        # Summary
        total = result.get('total_items_processed', 0)
        successful = result.get('successful_items', 0)
        failed = result.get('failed_items', 0)
        
        print(f"Total Items Processed: {total}")
        print(f"Successful: {successful} ✅")
        print(f"Failed: {failed} ❌")
        print(f"Success Rate: {(successful/total*100) if total > 0 else 0:.1f}%")
        print(f"Hours Back: {result.get('hours_back', 'N/A')}")
        print(f"Execution Time: {result.get('execution_time_seconds', 0):.2f}s")
        
        # Individual results
        individual_results = result.get('results', [])
        if individual_results:
            print(f"\n📋 INDIVIDUAL RESULTS")
            print("-" * 30)
            for i, item_result in enumerate(individual_results, 1):
                work_item_id = item_result.get('work_item_id', 'N/A')
                success = item_result.get('success', False)
                email_sent = item_result.get('email_sent', False)
                teams_sent = item_result.get('teams_sent', False)
                
                status = "✅" if success else "❌"
                print(f"  {i}. Work Item {work_item_id}: {status}")
                print(f"     Email: {'✅' if email_sent else '❌'} | Teams: {'✅' if teams_sent else '❌'}")
                
                errors = item_result.get('errors', [])
                if errors:
                    print(f"     Errors: {', '.join(errors)}")
    
    async def run_demo(self):
        """Run a comprehensive demonstration of the workflow."""
        print("🎯 AutoDefectTriage Step-by-Step Workflow Demo")
        print("=" * 60)
        
        print("\n📝 This demo will show you how the workflow processes work items")
        print("through all 5 steps:")
        print("  1. Work Item Creation/Retrieval")
        print("  2. Historical Analysis & Pattern Recognition")
        print("  3. AI Triage & Message Generation")
        print("  4. Email Notification Delivery")
        print("  5. Teams Message Broadcasting")
        
        # Demo single work item
        print(f"\n🔍 DEMO 1: Single Work Item Processing")
        await self.execute_single_workflow("demo-12345")
        
        # Demo batch processing
        print(f"\n📦 DEMO 2: Batch Processing")
        await self.execute_batch_workflow(1)  # Last 1 hour
        
        print(f"\n✨ Demo completed! Check the logs for detailed execution information.")


async def main():
    """Main entry point for the demo script."""
    parser = argparse.ArgumentParser(
        description="AutoDefectTriage Step-by-Step Workflow Demo"
    )
    parser.add_argument(
        "--single", 
        type=str, 
        help="Process a single work item by ID"
    )
    parser.add_argument(
        "--batch", 
        type=int, 
        help="Process work items from last N hours"
    )
    parser.add_argument(
        "--demo", 
        action="store_true", 
        help="Run comprehensive demo"
    )
    parser.add_argument(
        "--url", 
        type=str, 
        default="https://your-function-app.azurewebsites.net",
        help="Azure Function app URL"
    )
    parser.add_argument(
        "--key", 
        type=str, 
        help="Function key for authentication"
    )
    
    args = parser.parse_args()
    
    if not any([args.single, args.batch, args.demo]):
        parser.print_help()
        return
    
    # Initialize demo client
    demo = WorkflowDemo(args.url, args.key)
    
    try:
        if args.single:
            await demo.execute_single_workflow(args.single)
        elif args.batch:
            await demo.execute_batch_workflow(args.batch)
        elif args.demo:
            await demo.run_demo()
    
    except Exception as e:
        print(f"\n❌ Demo failed: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
