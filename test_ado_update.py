"""
Test script to verify ADO client can update work items.
"""

import asyncio
import sys
import os

# Add the functions directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'functions'))

from functions.__app__.common.adapters.ado_client import AdoClient
from functions.__app__.common.utils.config import get_config

async def test_ado_update():
    """Test ADO client update functionality."""
    try:
        # Get configuration
        config = get_config()
        
        # Initialize ADO client
        ado_client = AdoClient(config)
        
        # Test work item ID (replace with a real work item ID from your project)
        test_work_item_id = 12345  # Replace with actual work item ID
        
        print(f"Testing ADO client with work item {test_work_item_id}")
        
        # First, try to get the work item
        print("1. Getting work item...")
        work_item = await ado_client.get_work_item(test_work_item_id)
        
        if work_item:
            print(f"✅ Successfully retrieved work item: {work_item.get('fields', {}).get('System.Title', 'No title')}")
            
            # Try to add a comment
            print("2. Adding test comment...")
            test_comment = f"🧪 Test comment from defect feedback system - {asyncio.get_event_loop().time()}"
            
            updates = {
                "System.History": test_comment
            }
            
            updated_work_item = await ado_client.update_work_item(test_work_item_id, updates)
            
            if updated_work_item:
                print("✅ Successfully updated work item with test comment")
                return True
            else:
                print("❌ Failed to update work item")
                return False
        else:
            print(f"❌ Could not retrieve work item {test_work_item_id}")
            return False
            
    except Exception as e:
        print(f"❌ Error during ADO test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_response_processing():
    """Test the response processing pipeline."""
    try:
        from functions.__app__.common.services.response_processing_pipeline import ResponseProcessingPipeline
        
        config = get_config()
        pipeline = ResponseProcessingPipeline(config)
        
        # Test data matching your format
        test_work_item_id = 12345  # Replace with actual work item ID
        test_response_data = {
            "replyText": "This looks correct, I accept the AI triage recommendations",
            "priority": 2,
            "user_email": "<EMAIL>",
            "user_name": "Test User"
        }
        
        print(f"Testing response processing pipeline with work item {test_work_item_id}")
        
        success, result = await pipeline.process_response(
            test_work_item_id,
            test_response_data,
            "logic_app"
        )
        
        if success:
            print("✅ Response processing pipeline completed successfully")
            print(f"Result: {result}")
            return True
        else:
            print("❌ Response processing pipeline failed")
            print(f"Error: {result.get('error_message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error during response processing test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing ADO Update Functionality")
    print("=" * 50)
    
    # Run the tests
    loop = asyncio.get_event_loop()
    
    print("\n📋 Test 1: Direct ADO Client Test")
    ado_success = loop.run_until_complete(test_ado_update())
    
    print("\n🔄 Test 2: Response Processing Pipeline Test")
    pipeline_success = loop.run_until_complete(test_response_processing())
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"ADO Client Test: {'✅ PASS' if ado_success else '❌ FAIL'}")
    print(f"Pipeline Test: {'✅ PASS' if pipeline_success else '❌ FAIL'}")
    
    if ado_success and pipeline_success:
        print("\n🎉 All tests passed! The system should be working correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the configuration and error messages above.")
