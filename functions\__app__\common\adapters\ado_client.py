"""
Azure DevOps REST API Client
Handles work item operations, queries, and updates.
"""

import json
import logging
from typing import Dict, List, Any, Optional
import httpx
from datetime import datetime

from ..utils.config import Config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class AdoClient:
    """Client for Azure DevOps REST API operations."""
    
    def __init__(self, config: Config):
        self.config = config
        self.base_url = f"https://dev.azure.com/{config.ADO_ORGANIZATION}"
        self.project = config.ADO_PROJECT
        self.pat_token = config.ADO_PAT_TOKEN
        
        # Setup HTTP client with authentication
        self.client = httpx.AsyncClient(
            headers={
                "Authorization": f"Basic {self._encode_pat_token()}",
                "Content-Type": "application/json",
                "Accept": "application/json"
            },
            timeout=30.0
        )
    
    def _encode_pat_token(self) -> str:
        """Encode PAT token for basic auth."""
        import base64
        token_bytes = f":{self.pat_token}".encode('ascii')
        return base64.b64encode(token_bytes).decode('ascii')
    
    async def get_work_item(self, work_item_id: int, expand: str = "all") -> Optional[Dict[str, Any]]:
        """
        Get a single work item by ID.
        
        Args:
            work_item_id: The work item ID
            expand: Fields to expand (relations, fields, etc.)
        
        Returns:
            Work item data or None if not found
        """
        try:
            url = f"{self.base_url}/{self.project}/_apis/wit/workitems/{work_item_id}"
            params = {
                "$expand": expand,
                "api-version": "7.0"
            }
            
            response = await self.client.get(url, params=params)
            response.raise_for_status()
            
            work_item = response.json()
            
            log_structured(
                logger,
                "debug",
                "Retrieved work item",
                extra={
                    "work_item_id": work_item_id,
                    "title": work_item.get("fields", {}).get("System.Title", "")[:50]
                }
            )
            
            return work_item
            
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                logger.warning(f"Work item {work_item_id} not found")
                return None
            logger.error(f"HTTP error getting work item {work_item_id}: {e}")
            raise
        except Exception as e:
            logger.error(f"Error getting work item {work_item_id}: {e}")
            raise
    
    async def query_work_items(self, wiql_query: str) -> List[Dict[str, Any]]:
        """
        Execute a WIQL query to get work items.
        
        Args:
            wiql_query: The WIQL query string
        
        Returns:
            List of work items
        """
        try:
            # First, execute the query to get work item IDs
            query_url = f"{self.base_url}/{self.project}/_apis/wit/wiql"
            query_params = {"api-version": "7.0"}
            query_body = {"query": wiql_query}
            
            response = await self.client.post(
                query_url, 
                params=query_params, 
                json=query_body
            )
            response.raise_for_status()
            
            query_result = response.json()
            work_item_refs = query_result.get("workItems", [])
            
            if not work_item_refs:
                return []
            
            # Extract work item IDs
            work_item_ids = [str(ref["id"]) for ref in work_item_refs]
            
            # Batch get work items (max 200 at a time)
            work_items = []
            batch_size = 200
            
            for i in range(0, len(work_item_ids), batch_size):
                batch_ids = work_item_ids[i:i + batch_size]
                batch_url = f"{self.base_url}/{self.project}/_apis/wit/workitems"
                batch_params = {
                    "ids": ",".join(batch_ids),
                    "$expand": "fields",
                    "api-version": "7.0"
                }
                
                batch_response = await self.client.get(batch_url, params=batch_params)
                batch_response.raise_for_status()
                
                batch_result = batch_response.json()
                work_items.extend(batch_result.get("value", []))
            
            log_structured(
                logger,
                "info",
                "Executed WIQL query",
                extra={
                    "query_length": len(wiql_query),
                    "result_count": len(work_items)
                }
            )
            
            return work_items
            
        except Exception as e:
            logger.error(f"Error executing WIQL query: {e}")
            raise
    
    async def update_work_item(
        self, 
        work_item_id: int, 
        updates: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update a work item with the specified field values.
        
        Args:
            work_item_id: The work item ID
            updates: Dictionary of field names to values
        
        Returns:
            Updated work item data
        """
        try:
            url = f"{self.base_url}/{self.project}/_apis/wit/workitems/{work_item_id}"
            params = {"api-version": "7.0"}
            
            # Build JSON Patch operations
            patch_operations = []
            for field_name, value in updates.items():
                patch_operations.append({
                    "op": "add",
                    "path": f"/fields/{field_name}",
                    "value": value
                })
            
            response = await self.client.patch(
                url,
                params=params,
                json=patch_operations,
                headers={"Content-Type": "application/json-patch+json"}
            )
            response.raise_for_status()
            
            updated_work_item = response.json()
            
            log_structured(
                logger,
                "info",
                "Updated work item",
                extra={
                    "work_item_id": work_item_id,
                    "updated_fields": list(updates.keys())
                }
            )
            
            return updated_work_item
            
        except Exception as e:
            logger.error(f"Error updating work item {work_item_id}: {e}")
            raise
    
    async def add_comment(self, work_item_id: int, comment: str) -> Dict[str, Any]:
        """
        Add a comment to a work item.
        
        Args:
            work_item_id: The work item ID
            comment: The comment text
        
        Returns:
            Comment data
        """
        try:
            url = f"{self.base_url}/{self.project}/_apis/wit/workitems/{work_item_id}/comments"
            params = {"api-version": "7.0-preview.3"}
            
            comment_body = {
                "text": comment
            }
            
            response = await self.client.post(
                url,
                params=params,
                json=comment_body
            )
            response.raise_for_status()
            
            comment_result = response.json()
            
            log_structured(
                logger,
                "info",
                "Added comment to work item",
                extra={
                    "work_item_id": work_item_id,
                    "comment_length": len(comment)
                }
            )
            
            return comment_result
            
        except Exception as e:
            logger.error(f"Error adding comment to work item {work_item_id}: {e}")
            raise
    
    async def assign_work_item(self, work_item_id: int, assignee: str) -> Dict[str, Any]:
        """
        Assign a work item to a user.
        
        Args:
            work_item_id: The work item ID
            assignee: The assignee email or display name
        
        Returns:
            Updated work item data
        """
        return await self.update_work_item(
            work_item_id,
            {"System.AssignedTo": assignee}
        )
    
    async def get_team_members(self, team_name: str) -> List[Dict[str, Any]]:
        """
        Get members of a specific team.
        
        Args:
            team_name: The team name
        
        Returns:
            List of team members
        """
        try:
            url = f"{self.base_url}/{self.project}/_apis/projects/{self.project}/teams/{team_name}/members"
            params = {"api-version": "7.0"}
            
            response = await self.client.get(url, params=params)
            response.raise_for_status()
            
            result = response.json()
            members = result.get("value", [])
            
            log_structured(
                logger,
                "debug",
                "Retrieved team members",
                extra={
                    "team_name": team_name,
                    "member_count": len(members)
                }
            )
            
            return members
            
        except Exception as e:
            logger.error(f"Error getting team members for {team_name}: {e}")
            raise
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
