"""
Defect Feedback Service
Handles user feedback and team responses to update defects back with team logic.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from ..models.schemas import WorkItem, TriageResult
from ..adapters.ado_client import AdoClient
from ..adapters.teams_client import TeamsClient
from ..adapters.search_client import Search<PERSON>lient
from ..utils.config import Config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class FeedbackType(Enum):
    """Types of feedback that can be provided."""
    ASSIGNMENT_FEEDBACK = "assignment_feedback"
    PRIORITY_FEEDBACK = "priority_feedback"
    DUPLICATE_FEEDBACK = "duplicate_feedback"
    RESOLUTION_FEEDBACK = "resolution_feedback"
    GENERAL_FEEDBACK = "general_feedback"


class FeedbackSentiment(Enum):
    """Sentiment of the feedback."""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    CORRECTION = "correction"


@dataclass
class UserFeedback:
    """Represents user feedback on a work item."""
    work_item_id: int
    user_email: str
    user_name: str
    feedback_type: FeedbackType
    sentiment: FeedbackSentiment
    content: str
    metadata: Dict[str, Any]
    timestamp: datetime
    processed: bool = False


class DefectFeedbackService:
    """Service to handle defect feedback and update work items with team responses."""
    
    def __init__(self, config: Config):
        self.config = config
        self.ado_client = AdoClient(config)
        self.teams_client = TeamsClient(config)
        self.search_client = SearchClient(config)
    
    async def process_user_feedback(
        self, 
        work_item_id: int, 
        feedback_data: Dict[str, Any]
    ) -> bool:
        """
        Process user feedback and update the work item accordingly.
        
        Args:
            work_item_id: The work item ID
            feedback_data: Dictionary containing feedback information
            
        Returns:
            True if feedback was processed successfully
        """
        try:
            # Parse feedback data
            feedback = self._parse_feedback_data(work_item_id, feedback_data)
            
            # Get current work item
            work_item_data = await self.ado_client.get_work_item(work_item_id)
            work_item = self._convert_to_work_item(work_item_data)
            
            # Process feedback based on type
            success = await self._process_feedback_by_type(work_item, feedback)
            
            if success:
                # Update feedback as processed
                feedback.processed = True
                
                # Log successful processing
                log_structured(
                    logger,
                    "info",
                    f"Successfully processed {feedback.feedback_type.value} feedback for work item {work_item_id}",
                    extra={
                        "work_item_id": work_item_id,
                        "feedback_type": feedback.feedback_type.value,
                        "user": feedback.user_name,
                        "sentiment": feedback.sentiment.value
                    }
                )
                
                # Send confirmation to user
                await self._send_feedback_confirmation(work_item, feedback)
            
            return success
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to process user feedback for work item {work_item_id}: {e}",
                extra={"work_item_id": work_item_id, "feedback_data": feedback_data},
                exc_info=True
            )
            return False
    
    async def collect_team_response(
        self, 
        work_item_id: int, 
        team_response_data: Dict[str, Any]
    ) -> bool:
        """
        Collect and process team responses to defect triage.
        
        Args:
            work_item_id: The work item ID
            team_response_data: Dictionary containing team response information
            
        Returns:
            True if team response was processed successfully
        """
        try:
            # Get current work item
            work_item_data = await self.ado_client.get_work_item(work_item_id)
            work_item = self._convert_to_work_item(work_item_data)
            
            # Process team response
            response_type = team_response_data.get("response_type", "general")
            team_name = team_response_data.get("team_name", "Unknown Team")
            response_content = team_response_data.get("content", "")
            responder = team_response_data.get("responder", "Unknown User")
            
            # Create team response comment
            comment = f"👥 Team Response from {team_name}"
            comment += f"\n📝 Response by: {responder}"
            comment += f"\n📋 Response type: {response_type.replace('_', ' ').title()}"
            comment += f"\n💬 Response: {response_content}"
            comment += f"\n⏰ Responded at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}"
            
            # Update work item with team response
            updates = {"System.History": comment}
            
            # Handle specific response types
            if response_type == "assignment_accepted":
                updates["System.State"] = "Active"
                comment += "\n✅ Assignment accepted by team"
            elif response_type == "assignment_rejected":
                updates["System.AssignedTo"] = ""
                comment += "\n❌ Assignment rejected - needs reassignment"
            elif response_type == "priority_change":
                new_priority = team_response_data.get("new_priority")
                if new_priority:
                    updates["Microsoft.VSTS.Common.Priority"] = new_priority
                    comment += f"\n📈 Priority changed to P{new_priority}"
            elif response_type == "resolution_provided":
                updates["System.State"] = "Resolved"
                comment += "\n🎯 Resolution provided by team"
            
            await self.ado_client.update_work_item(work_item_id, updates)
            
            # Send notification about team response
            await self._send_team_response_notification(work_item, team_response_data)
            
            log_structured(
                logger,
                "info",
                f"Processed team response for work item {work_item_id}",
                extra={
                    "work_item_id": work_item_id,
                    "team_name": team_name,
                    "response_type": response_type,
                    "responder": responder
                }
            )
            
            return True
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to process team response for work item {work_item_id}: {e}",
                extra={"work_item_id": work_item_id, "team_response_data": team_response_data},
                exc_info=True
            )
            return False
    
    def _parse_feedback_data(self, work_item_id: int, feedback_data: Dict[str, Any]) -> UserFeedback:
        """Parse raw feedback data into UserFeedback object."""
        feedback_type_str = feedback_data.get("feedback_type", "general_feedback")
        sentiment_str = feedback_data.get("sentiment", "neutral")
        
        try:
            feedback_type = FeedbackType(feedback_type_str)
        except ValueError:
            feedback_type = FeedbackType.GENERAL_FEEDBACK
        
        try:
            sentiment = FeedbackSentiment(sentiment_str)
        except ValueError:
            sentiment = FeedbackSentiment.NEUTRAL
        
        return UserFeedback(
            work_item_id=work_item_id,
            user_email=feedback_data.get("user_email", ""),
            user_name=feedback_data.get("user_name", "Unknown User"),
            feedback_type=feedback_type,
            sentiment=sentiment,
            content=feedback_data.get("content", ""),
            metadata=feedback_data.get("metadata", {}),
            timestamp=datetime.utcnow()
        )
    
    def _convert_to_work_item(self, work_item_data: Dict[str, Any]) -> WorkItem:
        """Convert ADO work item data to WorkItem object."""
        fields = work_item_data.get("fields", {})
        
        return WorkItem(
            id=work_item_data.get("id"),
            title=fields.get("System.Title", ""),
            description=fields.get("System.Description", ""),
            work_item_type=fields.get("System.WorkItemType", ""),
            state=fields.get("System.State", ""),
            assigned_to=fields.get("System.AssignedTo", {}).get("displayName", ""),
            created_by=fields.get("System.CreatedBy", {}).get("displayName", ""),
            created_date=fields.get("System.CreatedDate", ""),
            changed_date=fields.get("System.ChangedDate", ""),
            area_path=fields.get("System.AreaPath", ""),
            iteration_path=fields.get("System.IterationPath", ""),
            tags=fields.get("System.Tags", ""),
            priority=fields.get("Microsoft.VSTS.Common.Priority", 3),
            severity=fields.get("Microsoft.VSTS.Common.Severity", ""),
            project=work_item_data.get("fields", {}).get("System.TeamProject", "")
        )

    async def _process_feedback_by_type(self, work_item: WorkItem, feedback: UserFeedback) -> bool:
        """Process feedback based on its type."""
        try:
            if feedback.feedback_type == FeedbackType.ASSIGNMENT_FEEDBACK:
                return await self._process_assignment_feedback(work_item, feedback)
            elif feedback.feedback_type == FeedbackType.PRIORITY_FEEDBACK:
                return await self._process_priority_feedback(work_item, feedback)
            elif feedback.feedback_type == FeedbackType.DUPLICATE_FEEDBACK:
                return await self._process_duplicate_feedback(work_item, feedback)
            elif feedback.feedback_type == FeedbackType.RESOLUTION_FEEDBACK:
                return await self._process_resolution_feedback(work_item, feedback)
            else:
                return await self._process_general_feedback(work_item, feedback)

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to process {feedback.feedback_type.value} feedback: {e}",
                extra={"work_item_id": work_item.id, "feedback_type": feedback.feedback_type.value},
                exc_info=True
            )
            return False

    async def _process_assignment_feedback(self, work_item: WorkItem, feedback: UserFeedback) -> bool:
        """Process assignment-related feedback."""
        comment = f"👤 Assignment Feedback from {feedback.user_name}"
        comment += f"\n💬 Feedback: {feedback.content}"
        comment += f"\n😊 Sentiment: {feedback.sentiment.value.title()}"

        updates = {"System.History": comment}

        # Handle different sentiments
        if feedback.sentiment == FeedbackSentiment.NEGATIVE:
            # User disagrees with assignment
            comment += "\n❌ User disagrees with current assignment"
            if "reassign" in feedback.content.lower():
                updates["System.AssignedTo"] = ""
                comment += "\n🔄 Assignment cleared for reassignment"
        elif feedback.sentiment == FeedbackSentiment.POSITIVE:
            # User agrees with assignment
            comment += "\n✅ User agrees with current assignment"
            if work_item.state.lower() in ["new", "to do"]:
                updates["System.State"] = "Active"
                comment += "\n🚀 State changed to Active"
        elif feedback.sentiment == FeedbackSentiment.CORRECTION:
            # User provides correction
            suggested_assignee = feedback.metadata.get("suggested_assignee")
            if suggested_assignee:
                updates["System.AssignedTo"] = suggested_assignee
                comment += f"\n🎯 Assignment corrected to: {suggested_assignee}"

        comment += f"\n⏰ Feedback provided at: {feedback.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}"
        updates["System.History"] = comment

        await self.ado_client.update_work_item(work_item.id, updates)
        return True

    async def _process_priority_feedback(self, work_item: WorkItem, feedback: UserFeedback) -> bool:
        """Process priority-related feedback."""
        comment = f"📈 Priority Feedback from {feedback.user_name}"
        comment += f"\n💬 Feedback: {feedback.content}"
        comment += f"\n😊 Sentiment: {feedback.sentiment.value.title()}"

        updates = {"System.History": comment}

        # Handle priority corrections
        if feedback.sentiment == FeedbackSentiment.CORRECTION:
            suggested_priority = feedback.metadata.get("suggested_priority")
            if suggested_priority:
                updates["Microsoft.VSTS.Common.Priority"] = suggested_priority
                comment += f"\n🎯 Priority corrected to: P{suggested_priority}"
        elif feedback.sentiment == FeedbackSentiment.NEGATIVE:
            comment += "\n❌ User disagrees with current priority assessment"
        elif feedback.sentiment == FeedbackSentiment.POSITIVE:
            comment += "\n✅ User agrees with current priority assessment"

        comment += f"\n⏰ Feedback provided at: {feedback.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}"
        updates["System.History"] = comment

        await self.ado_client.update_work_item(work_item.id, updates)
        return True

    async def _process_duplicate_feedback(self, work_item: WorkItem, feedback: UserFeedback) -> bool:
        """Process duplicate-related feedback."""
        comment = f"🔄 Duplicate Feedback from {feedback.user_name}"
        comment += f"\n💬 Feedback: {feedback.content}"
        comment += f"\n😊 Sentiment: {feedback.sentiment.value.title()}"

        updates = {"System.History": comment}

        # Handle duplicate feedback
        if feedback.sentiment == FeedbackSentiment.POSITIVE:
            # User confirms it's a duplicate
            duplicate_of_id = feedback.metadata.get("duplicate_of_id")
            if duplicate_of_id:
                updates["System.State"] = "Closed"
                updates["System.Reason"] = "Duplicate"
                comment += f"\n✅ Confirmed as duplicate of work item #{duplicate_of_id}"
        elif feedback.sentiment == FeedbackSentiment.NEGATIVE:
            # User disagrees it's a duplicate
            comment += "\n❌ User disagrees with duplicate assessment"
            comment += "\n🔍 Requires further investigation"

        comment += f"\n⏰ Feedback provided at: {feedback.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}"
        updates["System.History"] = comment

        await self.ado_client.update_work_item(work_item.id, updates)
        return True

    async def _process_resolution_feedback(self, work_item: WorkItem, feedback: UserFeedback) -> bool:
        """Process resolution-related feedback."""
        comment = f"🎯 Resolution Feedback from {feedback.user_name}"
        comment += f"\n💬 Feedback: {feedback.content}"
        comment += f"\n😊 Sentiment: {feedback.sentiment.value.title()}"

        updates = {"System.History": comment}

        # Handle resolution feedback
        if feedback.sentiment == FeedbackSentiment.POSITIVE:
            comment += "\n✅ User satisfied with resolution"
            if work_item.state.lower() not in ["closed", "done"]:
                updates["System.State"] = "Closed"
                comment += "\n🏁 Work item closed based on positive feedback"
        elif feedback.sentiment == FeedbackSentiment.NEGATIVE:
            comment += "\n❌ User not satisfied with resolution"
            if work_item.state.lower() in ["closed", "done", "resolved"]:
                updates["System.State"] = "Active"
                comment += "\n🔄 Work item reopened for further work"

        comment += f"\n⏰ Feedback provided at: {feedback.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}"
        updates["System.History"] = comment

        await self.ado_client.update_work_item(work_item.id, updates)
        return True

    async def _process_general_feedback(self, work_item: WorkItem, feedback: UserFeedback) -> bool:
        """Process general feedback."""
        comment = f"💬 General Feedback from {feedback.user_name}"
        comment += f"\n💬 Feedback: {feedback.content}"
        comment += f"\n😊 Sentiment: {feedback.sentiment.value.title()}"
        comment += f"\n⏰ Feedback provided at: {feedback.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}"

        updates = {"System.History": comment}

        await self.ado_client.update_work_item(work_item.id, updates)
        return True

    async def process_teams_adaptive_card_response(
        self,
        work_item_id: int,
        response_data: Dict[str, Any]
    ) -> bool:
        """
        Process response from Teams adaptive card with replyText and priority.

        Args:
            work_item_id: The work item ID
            response_data: Response data containing replyText and priority

        Returns:
            True if response was processed successfully
        """
        try:
            # Extract response data
            reply_text = response_data.get("replyText", "")
            priority = response_data.get("priority")
            user_email = response_data.get("user_email", "")
            user_name = response_data.get("user_name", "Unknown User")

            # Get current work item
            work_item_data = await self.ado_client.get_work_item(work_item_id)
            work_item = self._convert_to_work_item(work_item_data)

            # Build update comment
            comment = f"📱 Teams Response from {user_name}"
            comment += f"\n💬 Reply: {reply_text}"

            # Prepare updates
            updates = {}

            # Handle priority update if provided
            if priority is not None:
                try:
                    priority_value = int(priority)
                    current_priority = work_item.priority or 3

                    if priority_value != current_priority:
                        updates["Microsoft.VSTS.Common.Priority"] = priority_value
                        comment += f"\n📈 Priority updated from P{current_priority} to P{priority_value}"
                    else:
                        comment += f"\n📈 Priority confirmed as P{priority_value}"

                except (ValueError, TypeError):
                    log_structured(
                        logger,
                        "warning",
                        f"Invalid priority value received: {priority}",
                        extra={"work_item_id": work_item_id, "priority": priority}
                    )

            # Handle reply text
            if reply_text:
                # Analyze reply text for actions
                reply_lower = reply_text.lower()

                if any(word in reply_lower for word in ["accept", "agree", "correct", "good"]):
                    comment += "\n✅ User accepts the AI triage recommendations"
                    if work_item.state.lower() in ["new", "to do"]:
                        updates["System.State"] = "Active"
                        comment += "\n🚀 State changed to Active"

                elif any(word in reply_lower for word in ["reject", "disagree", "wrong", "incorrect"]):
                    comment += "\n❌ User disagrees with AI triage recommendations"
                    comment += "\n🔍 Manual review required"

                elif any(word in reply_lower for word in ["reassign", "different team", "wrong team"]):
                    comment += "\n🔄 User requests reassignment"
                    updates["System.AssignedTo"] = ""

                elif any(word in reply_lower for word in ["duplicate", "already exists", "seen before"]):
                    comment += "\n🔄 User indicates this might be a duplicate"
                    comment += "\n🔍 Duplicate investigation needed"

                elif any(word in reply_lower for word in ["urgent", "critical", "high priority"]):
                    if not priority:  # Only update if priority wasn't explicitly set
                        updates["Microsoft.VSTS.Common.Priority"] = 1
                        comment += "\n🚨 Priority elevated to P1 based on user feedback"

                elif any(word in reply_lower for word in ["low priority", "not urgent", "can wait"]):
                    if not priority:  # Only update if priority wasn't explicitly set
                        updates["Microsoft.VSTS.Common.Priority"] = 4
                        comment += "\n📉 Priority lowered to P4 based on user feedback"

            comment += f"\n⏰ Response received at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}"

            # Add comment to updates
            updates["System.History"] = comment

            # Update work item
            await self.ado_client.update_work_item(work_item_id, updates)

            # Send confirmation back to user
            await self._send_response_confirmation(work_item_id, user_name, reply_text, priority)

            log_structured(
                logger,
                "info",
                f"Processed Teams adaptive card response for work item {work_item_id}",
                extra={
                    "work_item_id": work_item_id,
                    "user_name": user_name,
                    "reply_text": reply_text,
                    "priority": priority,
                    "updates_applied": list(updates.keys())
                }
            )

            return True

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to process Teams adaptive card response for work item {work_item_id}: {e}",
                extra={"work_item_id": work_item_id, "response_data": response_data},
                exc_info=True
            )
            return False

    async def _send_response_confirmation(
        self,
        work_item_id: int,
        user_name: str,
        reply_text: str,
        priority: Optional[int]
    ) -> bool:
        """Send confirmation that the response was processed."""
        try:
            confirmation_message = f"✅ **Response Processed Successfully**\n\n"
            confirmation_message += f"Your response to work item #{work_item_id} has been processed:\n\n"

            if reply_text:
                confirmation_message += f"**Your Reply:** {reply_text}\n\n"

            if priority is not None:
                confirmation_message += f"**Priority Updated:** P{priority}\n\n"

            confirmation_message += f"**Processed by:** {user_name}\n"
            confirmation_message += f"**Processed at:** {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}\n\n"
            confirmation_message += "The work item has been updated with your feedback. "
            confirmation_message += "You can view the latest updates in Azure DevOps."

            # Send via Teams Logic App
            await self.teams_client._send_simple_message(
                user_name,
                f"Response Confirmation - Work Item {work_item_id}",
                confirmation_message
            )

            return True

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to send response confirmation: {e}",
                extra={
                    "work_item_id": work_item_id,
                    "user_name": user_name
                },
                exc_info=True
            )
            return False

    async def _send_feedback_confirmation(self, work_item: WorkItem, feedback: UserFeedback) -> bool:
        """Send confirmation that feedback was processed."""
        try:
            confirmation_message = f"✅ **Feedback Processed Successfully**\n\n"
            confirmation_message += f"Your {feedback.feedback_type.value.replace('_', ' ').title()} "
            confirmation_message += f"for work item #{work_item.id} has been processed.\n\n"
            confirmation_message += f"**Your Feedback:** {feedback.content}\n\n"
            confirmation_message += f"**Sentiment:** {feedback.sentiment.value.title()}\n\n"
            confirmation_message += f"**Processed at:** {feedback.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}\n\n"
            confirmation_message += "The work item has been updated based on your feedback. "
            confirmation_message += "Thank you for helping improve our AI triage system!"

            # Send via Teams Logic App
            await self.teams_client._send_simple_message(
                feedback.user_name,
                f"Feedback Confirmation - Work Item {work_item.id}",
                confirmation_message
            )

            return True

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to send feedback confirmation: {e}",
                extra={
                    "work_item_id": work_item.id,
                    "user_name": feedback.user_name
                },
                exc_info=True
            )
            return False

    async def _send_team_response_notification(
        self,
        work_item: WorkItem,
        team_response_data: Dict[str, Any]
    ) -> bool:
        """Send notification about team response."""
        try:
            team_name = team_response_data.get("team_name", "Unknown Team")
            responder = team_response_data.get("responder", "Unknown User")
            response_type = team_response_data.get("response_type", "general")

            notification_message = f"👥 **Team Response Received**\n\n"
            notification_message += f"**Work Item:** #{work_item.id} - {work_item.title}\n\n"
            notification_message += f"**Team:** {team_name}\n"
            notification_message += f"**Responder:** {responder}\n"
            notification_message += f"**Response Type:** {response_type.replace('_', ' ').title()}\n\n"
            notification_message += f"**Response:** {team_response_data.get('content', '')}\n\n"
            notification_message += f"**Processed at:** {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}\n\n"
            notification_message += "The work item has been updated with the team's response."

            # Send to stakeholders
            await self.teams_client._send_simple_message(
                "Stakeholders",
                f"Team Response - Work Item {work_item.id}",
                notification_message
            )

            return True

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to send team response notification: {e}",
                extra={
                    "work_item_id": work_item.id,
                    "team_response_data": team_response_data
                },
                exc_info=True
            )
            return False
