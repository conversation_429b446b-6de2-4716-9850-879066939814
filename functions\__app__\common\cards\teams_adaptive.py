"""
Teams Adaptive Card builders for triage notifications.
"""

from typing import Dict, List, Any, Optional
from datetime import datetime

from ..models.schemas import WorkItem, TriageResult, DuplicateHit


def build_triage_card(work_item: WorkItem, triage_result: TriageResult) -> Dict[str, Any]:
    """
    Build an Adaptive Card for triage notification.
    
    Args:
        work_item: The work item that was triaged
        triage_result: The triage results
    
    Returns:
        Adaptive Card JSON structure
    """
    # Determine card color based on priority
    color = _get_priority_color(triage_result.priority or work_item.priority)
    
    # Build the card
    card = {
        "type": "message",
        "attachments": [
            {
                "contentType": "application/vnd.microsoft.card.adaptive",
                "content": {
                    "type": "AdaptiveCard",
                    "version": "1.4",
                    "body": [
                        {
                            "type": "Container",
                            "style": "emphasis",
                            "items": [
                                {
                                    "type": "ColumnSet",
                                    "columns": [
                                        {
                                            "type": "Column",
                                            "width": "auto",
                                            "items": [
                                                {
                                                    "type": "TextBlock",
                                                    "text": "🤖",
                                                    "size": "Large"
                                                }
                                            ]
                                        },
                                        {
                                            "type": "Column",
                                            "width": "stretch",
                                            "items": [
                                                {
                                                    "type": "TextBlock",
                                                    "text": "AI Triage Complete",
                                                    "weight": "Bolder",
                                                    "size": "Medium",
                                                    "color": color
                                                },
                                                {
                                                    "type": "TextBlock",
                                                    "text": f"Work Item #{work_item.id}",
                                                    "size": "Small",
                                                    "color": "Default",
                                                    "spacing": "None"
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "type": "Container",
                            "items": [
                                {
                                    "type": "TextBlock",
                                    "text": work_item.title,
                                    "weight": "Bolder",
                                    "size": "Medium",
                                    "wrap": True
                                },
                                {
                                    "type": "TextBlock",
                                    "text": _truncate_text(work_item.description or "", 200),
                                    "wrap": True,
                                    "spacing": "Small"
                                }
                            ]
                        }
                    ]
                }
            }
        ]
    }
    
    # Add triage results section
    triage_facts = []
    
    if triage_result.assigned_to:
        triage_facts.append({
            "title": "👤 Assigned To",
            "value": triage_result.assigned_to
        })
    
    if triage_result.priority:
        priority_text = _get_priority_text(triage_result.priority)
        triage_facts.append({
            "title": "⚡ Priority",
            "value": priority_text
        })
    
    if triage_result.duplicates:
        triage_facts.append({
            "title": "🔍 Duplicates",
            "value": f"{len(triage_result.duplicates)} potential duplicates found"
        })
    
    triage_facts.append({
        "title": "🎯 Confidence",
        "value": f"{triage_result.confidence_score:.1%}"
    })
    
    if triage_facts:
        card["attachments"][0]["content"]["body"].append({
            "type": "Container",
            "items": [
                {
                    "type": "TextBlock",
                    "text": "**Triage Results**",
                    "weight": "Bolder",
                    "size": "Medium"
                },
                {
                    "type": "FactSet",
                    "facts": triage_facts
                }
            ]
        })
    
    # Add reasoning section
    if triage_result.reasoning:
        card["attachments"][0]["content"]["body"].append({
            "type": "Container",
            "items": [
                {
                    "type": "TextBlock",
                    "text": "**Reasoning**",
                    "weight": "Bolder",
                    "size": "Medium"
                },
                {
                    "type": "TextBlock",
                    "text": triage_result.reasoning,
                    "wrap": True,
                    "size": "Small"
                }
            ]
        })
    
    # Add duplicates section if any
    if triage_result.duplicates:
        duplicates_items = [
            {
                "type": "TextBlock",
                "text": "**Potential Duplicates**",
                "weight": "Bolder",
                "size": "Medium"
            }
        ]
        
        for duplicate in triage_result.duplicates[:3]:  # Show max 3 duplicates
            duplicates_items.append({
                "type": "Container",
                "style": "accent",
                "items": [
                    {
                        "type": "TextBlock",
                        "text": f"#{duplicate.work_item_id}: {duplicate.title}",
                        "weight": "Bolder",
                        "size": "Small",
                        "wrap": True
                    },
                    {
                        "type": "TextBlock",
                        "text": f"Similarity: {duplicate.similarity_score:.1%}",
                        "size": "Small",
                        "color": "Accent"
                    }
                ]
            })
        
        card["attachments"][0]["content"]["body"].append({
            "type": "Container",
            "items": duplicates_items
        })
    
    # Add actions
    actions = []
    
    # View work item action (would need actual ADO URL)
    actions.append({
        "type": "Action.OpenUrl",
        "title": "View Work Item",
        "url": f"https://dev.azure.com/organization/project/_workitems/edit/{work_item.id}"
    })
    
    if triage_result.duplicates:
        actions.append({
            "type": "Action.OpenUrl",
            "title": "Review Duplicates",
            "url": f"https://dev.azure.com/organization/project/_workitems/edit/{work_item.id}"
        })
    
    if actions:
        card["attachments"][0]["content"]["actions"] = actions
    
    return card


def build_duplicate_alert_card(work_item: WorkItem, duplicate_items: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Build an Adaptive Card for duplicate detection alert.
    
    Args:
        work_item: The new work item
        duplicate_items: List of potential duplicate work items
    
    Returns:
        Adaptive Card JSON structure
    """
    card = {
        "type": "message",
        "attachments": [
            {
                "contentType": "application/vnd.microsoft.card.adaptive",
                "content": {
                    "type": "AdaptiveCard",
                    "version": "1.4",
                    "body": [
                        {
                            "type": "Container",
                            "style": "warning",
                            "items": [
                                {
                                    "type": "ColumnSet",
                                    "columns": [
                                        {
                                            "type": "Column",
                                            "width": "auto",
                                            "items": [
                                                {
                                                    "type": "TextBlock",
                                                    "text": "⚠️",
                                                    "size": "Large"
                                                }
                                            ]
                                        },
                                        {
                                            "type": "Column",
                                            "width": "stretch",
                                            "items": [
                                                {
                                                    "type": "TextBlock",
                                                    "text": "Potential Duplicates Detected",
                                                    "weight": "Bolder",
                                                    "size": "Medium",
                                                    "color": "Warning"
                                                },
                                                {
                                                    "type": "TextBlock",
                                                    "text": f"Work Item #{work_item.id}",
                                                    "size": "Small",
                                                    "color": "Default",
                                                    "spacing": "None"
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "type": "Container",
                            "items": [
                                {
                                    "type": "TextBlock",
                                    "text": "**New Work Item**",
                                    "weight": "Bolder",
                                    "size": "Medium"
                                },
                                {
                                    "type": "TextBlock",
                                    "text": work_item.title,
                                    "weight": "Bolder",
                                    "wrap": True
                                },
                                {
                                    "type": "TextBlock",
                                    "text": _truncate_text(work_item.description or "", 150),
                                    "wrap": True,
                                    "size": "Small"
                                }
                            ]
                        }
                    ]
                }
            }
        ]
    }
    
    # Add duplicates section
    duplicates_items = [
        {
            "type": "TextBlock",
            "text": f"**Found {len(duplicate_items)} Potential Duplicates**",
            "weight": "Bolder",
            "size": "Medium"
        }
    ]
    
    for i, duplicate in enumerate(duplicate_items[:5]):  # Show max 5 duplicates
        similarity_score = duplicate.get('similarity_score', 0.0)
        duplicates_items.append({
            "type": "Container",
            "style": "accent",
            "items": [
                {
                    "type": "TextBlock",
                    "text": f"#{duplicate.get('work_item_id', 'Unknown')}: {duplicate.get('title', 'No title')}",
                    "weight": "Bolder",
                    "size": "Small",
                    "wrap": True
                },
                {
                    "type": "TextBlock",
                    "text": f"Similarity: {similarity_score:.1%}",
                    "size": "Small",
                    "color": "Accent"
                }
            ]
        })
    
    card["attachments"][0]["content"]["body"].append({
        "type": "Container",
        "items": duplicates_items
    })
    
    # Add actions
    actions = [
        {
            "type": "Action.OpenUrl",
            "title": "Review New Item",
            "url": f"https://dev.azure.com/organization/project/_workitems/edit/{work_item.id}"
        },
        {
            "type": "Action.OpenUrl",
            "title": "Search Duplicates",
            "url": f"https://dev.azure.com/organization/project/_workitems"
        }
    ]
    
    card["attachments"][0]["content"]["actions"] = actions
    
    return card


def _get_priority_color(priority: Optional[int]) -> str:
    """Get color for priority level."""
    if priority == 1:
        return "Attention"  # Red for critical
    elif priority == 2:
        return "Warning"    # Orange for high
    elif priority == 3:
        return "Accent"     # Blue for medium
    else:
        return "Default"    # Default for low


def _get_priority_text(priority: int) -> str:
    """Get text representation of priority."""
    priority_map = {
        1: "🔴 Critical",
        2: "🟠 High",
        3: "🟡 Medium",
        4: "🟢 Low"
    }
    return priority_map.get(priority, f"Priority {priority}")


def _truncate_text(text: str, max_length: int) -> str:
    """Truncate text to maximum length."""
    if len(text) <= max_length:
        return text
    return text[:max_length - 3] + "..."
