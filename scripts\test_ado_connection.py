#!/usr/bin/env python3
"""
Test Azure DevOps connection and find real work items.
"""

import asyncio
import json
import os
import sys

# Add the functions directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'functions'))

from __app__.common.adapters.ado_client import AdoClient
from __app__.common.utils.config import get_config
from __app__.common.utils.logging import setup_logging


def load_local_settings():
    """Load environment variables from local.settings.json."""
    settings_file = os.path.join(os.path.dirname(__file__), '..', 'functions', 'local.settings.json')
    
    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r') as f:
                settings = json.load(f)
            
            # Set environment variables from the Values section
            if "Values" in settings:
                for key, value in settings["Values"].items():
                    os.environ[key] = str(value)
            
            print("✅ Environment variables loaded from local.settings.json")
            return True
        except Exception as e:
            print(f"⚠️  Warning: Could not load local.settings.json: {e}")
            return False
    else:
        print("⚠️  Warning: local.settings.json not found")
        return False


async def test_ado_connection():
    """Test ADO connection and find work items."""
    
    print("🔧 Testing Azure DevOps Connection")
    print("=" * 50)
    
    # Load environment variables
    if not load_local_settings():
        print("❌ Failed to load configuration")
        return
    
    # Setup logging
    setup_logging()
    
    try:
        # Initialize ADO client
        config = get_config()
        ado_client = AdoClient(config)
        
        print(f"🏢 Organization: {config.ADO_ORGANIZATION}")
        print(f"📁 Project: {config.ADO_PROJECT}")
        print(f"🔑 PAT Token: {'*' * 20}...{config.ADO_PAT_TOKEN[-4:] if config.ADO_PAT_TOKEN else 'Not set'}")
        
        # Test 1: Try to get a specific work item (try common IDs)
        print(f"\n🔍 Testing work item retrieval...")
        test_ids = [1, 2, 3, 4, 5, 10, 100, 1000]
        
        found_items = []
        for work_item_id in test_ids:
            try:
                print(f"   Testing ID {work_item_id}...", end="")
                work_item = await ado_client.get_work_item(work_item_id)
                if work_item:
                    fields = work_item.get('fields', {})
                    title = fields.get('System.Title', 'No title')
                    work_type = fields.get('System.WorkItemType', 'Unknown')
                    print(f" ✅ Found: {work_type} - {title[:50]}...")
                    found_items.append({
                        'id': work_item_id,
                        'title': title,
                        'type': work_type,
                        'state': fields.get('System.State', 'Unknown')
                    })
                else:
                    print(f" ❌ Not found")
            except Exception as e:
                print(f" ❌ Error: {str(e)[:50]}...")
        
        if found_items:
            print(f"\n✅ Found {len(found_items)} work items:")
            for item in found_items:
                print(f"   📋 ID {item['id']}: {item['type']} - {item['title'][:60]}...")
        else:
            print(f"\n⚠️  No work items found with test IDs")
        
        # Test 2: Try a simple WIQL query
        print(f"\n🔍 Testing WIQL query...")
        try:
            simple_query = "SELECT [System.Id] FROM WorkItems WHERE [System.WorkItemType] = 'Bug'"
            print(f"   Query: {simple_query}")
            
            work_items = await ado_client.query_work_items(simple_query)
            if work_items:
                print(f"   ✅ Query successful! Found {len(work_items)} items")
                for i, item in enumerate(work_items[:5]):
                    fields = item.get('fields', {})
                    title = fields.get('System.Title', 'No title')
                    print(f"      {i+1}. ID {item.get('id')}: {title[:50]}...")
                
                if len(work_items) > 5:
                    print(f"      ... and {len(work_items) - 5} more")
                    
                return work_items[0].get('id')  # Return first work item ID for testing
            else:
                print(f"   ⚠️  Query returned no results")
        except Exception as e:
            print(f"   ❌ Query failed: {e}")
        
        # Test 3: Try different work item types
        print(f"\n🔍 Testing different work item types...")
        work_types = ['Task', 'User Story', 'Feature', 'Epic', 'Issue']
        
        for work_type in work_types:
            try:
                query = f"SELECT [System.Id] FROM WorkItems WHERE [System.WorkItemType] = '{work_type}'"
                items = await ado_client.query_work_items(query)
                if items:
                    print(f"   ✅ {work_type}: {len(items)} items found")
                    if items:
                        return items[0].get('id')  # Return first found item
                else:
                    print(f"   ⚠️  {work_type}: No items found")
            except Exception as e:
                print(f"   ❌ {work_type}: Error - {str(e)[:50]}...")
        
        return None
        
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()
        return None


async def main():
    """Main function."""
    work_item_id = await test_ado_connection()
    
    if work_item_id:
        print(f"\n🎯 SUCCESS! Found work item ID: {work_item_id}")
        print(f"💡 You can now run:")
        print(f"   python scripts/execute_with_real_data.py {work_item_id}")
    else:
        print(f"\n❌ No work items found. Check your ADO configuration.")


if __name__ == "__main__":
    asyncio.run(main())
