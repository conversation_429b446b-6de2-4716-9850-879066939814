"""
Simple test to verify the update functionality step by step.
"""

import asyncio
import sys
import os
import json

# Add the functions directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'functions'))

async def test_step_by_step():
    """Test each component step by step."""
    
    print("🧪 Step-by-Step Testing")
    print("=" * 50)
    
    # Step 1: Check configuration
    print("\n📋 Step 1: Loading Configuration")
    try:
        from functions.__app__.common.utils.config import get_config
        config = get_config()
        
        print(f"✅ Config loaded")
        print(f"   ADO Organization: {config.ADO_ORGANIZATION}")
        print(f"   ADO Project: {config.ADO_PROJECT}")
        print(f"   PAT Token: {'SET' if config.ADO_PAT_TOKEN else 'NOT SET'}")
        
        if not config.ADO_PAT_TOKEN:
            print("❌ ADO_PAT_TOKEN is not set. Please set this environment variable.")
            return False
            
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return False
    
    # Step 2: Initialize ADO Client
    print("\n🔗 Step 2: Initializing ADO Client")
    try:
        from functions.__app__.common.adapters.ado_client import AdoClient
        ado_client = AdoClient(config)
        print("✅ ADO Client initialized")
        
    except Exception as e:
        print(f"❌ Error initializing ADO client: {e}")
        return False
    
    # Step 3: Test a simple work item query (to verify connection)
    print("\n🔍 Step 3: Testing ADO Connection")
    try:
        # Try to get any work item (we'll use a query to find one)
        # First, let's try a simple query to get work items
        
        # For now, let's just test if we can make a basic API call
        import httpx
        
        # Test basic connectivity
        test_url = f"{ado_client.base_url}/{ado_client.project}/_apis/wit/workitems"
        params = {
            "ids": "1,2,3,4,5",  # Try to get first few work items
            "api-version": "7.0",
            "$expand": "fields"
        }
        
        response = await ado_client.client.get(test_url, params=params)
        
        if response.status_code == 200:
            data = response.json()
            work_items = data.get("value", [])
            
            if work_items:
                test_work_item = work_items[0]
                test_work_item_id = test_work_item["id"]
                title = test_work_item.get("fields", {}).get("System.Title", "No title")
                
                print(f"✅ ADO connection successful")
                print(f"   Found work item {test_work_item_id}: {title[:50]}...")
                
                return test_work_item_id
            else:
                print("⚠️  ADO connection successful but no work items found")
                print("   You may need to create a work item first or check project permissions")
                return None
        else:
            print(f"❌ ADO API call failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing ADO connection: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_update_with_work_item(work_item_id):
    """Test updating a specific work item."""
    
    print(f"\n🔄 Step 4: Testing Work Item Update (ID: {work_item_id})")
    
    try:
        from functions.__app__.common.utils.config import get_config
        from functions.__app__.common.adapters.ado_client import AdoClient
        
        config = get_config()
        ado_client = AdoClient(config)
        
        # First get the current work item
        print("   Getting current work item...")
        work_item = await ado_client.get_work_item(work_item_id)
        
        if not work_item:
            print(f"❌ Could not retrieve work item {work_item_id}")
            return False
        
        current_title = work_item.get("fields", {}).get("System.Title", "No title")
        print(f"   Current title: {current_title[:50]}...")
        
        # Try to add a simple comment
        print("   Adding test comment...")
        
        test_comment = f"🧪 Test comment from AutoDefectTriage system - {asyncio.get_event_loop().time()}"
        
        updates = {
            "System.History": test_comment
        }
        
        updated_work_item = await ado_client.update_work_item(work_item_id, updates)
        
        if updated_work_item:
            print("✅ Work item updated successfully!")
            print(f"   Updated work item ID: {updated_work_item.get('id')}")
            return True
        else:
            print("❌ Work item update returned None")
            return False
            
    except Exception as e:
        print(f"❌ Error updating work item: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_response_processing_with_work_item(work_item_id):
    """Test the full response processing pipeline."""
    
    print(f"\n🔄 Step 5: Testing Response Processing Pipeline (ID: {work_item_id})")
    
    try:
        from functions.__app__.common.services.response_processing_pipeline import ResponseProcessingPipeline
        from functions.__app__.common.utils.config import get_config
        
        config = get_config()
        pipeline = ResponseProcessingPipeline(config)
        
        # Test response data in your format
        test_response_data = {
            "replyText": "This looks correct, I accept the AI triage recommendations. Great work!",
            "priority": 2,
            "user_email": "<EMAIL>",
            "user_name": "Test User"
        }
        
        print("   Processing test response...")
        print(f"   Response data: {json.dumps(test_response_data, indent=2)}")
        
        success, result = await pipeline.process_response(
            work_item_id,
            test_response_data,
            "logic_app"
        )
        
        if success:
            print("✅ Response processing completed successfully!")
            print(f"   Processing stages: {result.get('processing_stages', [])}")
            print(f"   Updates applied: {result.get('updates_applied', [])}")
            print(f"   Action ID: {result.get('action_id', 'None')}")
            return True
        else:
            print("❌ Response processing failed")
            print(f"   Error: {result.get('error_message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error in response processing: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    
    # Step 1-3: Test basic connectivity and find a work item
    work_item_id = await test_step_by_step()
    
    if work_item_id is False:
        print("\n❌ Basic connectivity test failed. Cannot proceed.")
        return
    elif work_item_id is None:
        print("\n⚠️  No work items found. Please create a work item first.")
        return
    
    # Step 4: Test direct work item update
    update_success = await test_update_with_work_item(work_item_id)
    
    if not update_success:
        print("\n❌ Direct work item update failed. Cannot proceed to pipeline test.")
        return
    
    # Step 5: Test full response processing pipeline
    pipeline_success = await test_response_processing_with_work_item(work_item_id)
    
    # Final summary
    print("\n" + "=" * 50)
    print("📊 Final Test Results:")
    print(f"✅ ADO Connection: PASS")
    print(f"✅ Direct Update: {'PASS' if update_success else 'FAIL'}")
    print(f"✅ Pipeline Processing: {'PASS' if pipeline_success else 'FAIL'}")
    
    if update_success and pipeline_success:
        print(f"\n🎉 All tests passed! The system is working correctly with work item {work_item_id}")
        print("\nYou can now test with your actual Teams responses using:")
        print(f"   Work Item ID: {work_item_id}")
        print("   Endpoint: /logic_app_response")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    asyncio.run(main())
