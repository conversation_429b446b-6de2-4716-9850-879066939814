#!/usr/bin/env python3
"""
Test Environment Loading
========================

This script tests how environment variables are loaded from local.settings.json.
"""

import json
import os
from pathlib import Path

def load_local_settings():
    """Load local.settings.json and set environment variables."""
    settings_file = Path("functions/local.settings.json")
    
    if not settings_file.exists():
        print(f"❌ Settings file not found: {settings_file}")
        return False
    
    try:
        with open(settings_file, 'r') as f:
            settings = json.load(f)
        
        print(f"✅ Loaded settings from: {settings_file}")
        
        # Set environment variables from the Values section
        if "Values" in settings:
            for key, value in settings["Values"].items():
                os.environ[key] = str(value)
                print(f"   Set {key} = {value[:50]}{'...' if len(str(value)) > 50 else ''}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading settings: {e}")
        return False

def test_environment_variables():
    """Test specific environment variables."""
    print("\n🔍 Testing Environment Variables:")
    
    test_vars = [
        "TEAMS_WEBHOOK_URL",
        "TEAMS_LOGIC_APP_URL", 
        "Email_LOGIC_APP_URL",
        "TEAMS_NOTIFICATIONS_ENABLED"
    ]
    
    for var in test_vars:
        value = os.environ.get(var, "NOT_SET")
        print(f"   {var}: '{value[:50]}{'...' if len(value) > 50 else ''}'")

def test_config_loading():
    """Test configuration loading after setting environment variables."""
    print("\n🔧 Testing Configuration Loading:")
    
    import sys
    sys.path.append(str(Path(__file__).parent.parent / "functions"))
    
    from __app__.common.utils.config import get_config
    from __app__.common.adapters.teams_client import TeamsClient
    
    config = get_config()
    
    print(f"   TEAMS_WEBHOOK_URL: '{config.TEAMS_WEBHOOK_URL}'")
    print(f"   TEAMS_LOGIC_APP_URL: '{config.TEAMS_LOGIC_APP_URL}'")
    print(f"   Email_LOGIC_APP_URL: '{config.Email_LOGIC_APP_URL}'")
    
    # Test Teams client
    teams_client = TeamsClient(config)
    print(f"   TeamsClient.teams_logic_app_url: '{teams_client.teams_logic_app_url}'")
    print(f"   TeamsClient.email_logic_app_url: '{teams_client.email_logic_app_url}'")
    
    # Test notification channel detection
    if teams_client.teams_logic_app_url:
        print("   ✅ Teams Logic App URL configured!")
    elif teams_client.webhook_url:
        print("   ✅ Teams Webhook URL configured!")
    elif teams_client.email_logic_app_url:
        print("   ✅ Email Logic App URL configured (fallback)!")
    else:
        print("   ❌ No notification channels configured")

def main():
    """Main test function."""
    print("🧪 Environment Loading Test")
    print("=" * 60)
    
    # Load local settings and set environment variables
    if load_local_settings():
        # Test environment variables
        test_environment_variables()
        
        # Test configuration loading
        test_config_loading()
        
        print("\n✨ Test completed!")
    else:
        print("\n❌ Test failed!")

if __name__ == "__main__":
    main()
