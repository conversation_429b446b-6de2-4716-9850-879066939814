#!/usr/bin/env python3
"""
Test Teams Configuration
========================

This script tests the Teams configuration to see what's being loaded.
"""

import sys
import os
from pathlib import Path

# Add the functions directory to the path
sys.path.append(str(Path(__file__).parent.parent / "functions"))

from __app__.common.utils.config import get_config
from __app__.common.adapters.teams_client import TeamsClient

def test_teams_config():
    """Test Teams configuration loading."""
    print("🔧 Testing Teams Configuration")
    print("=" * 60)
    
    # Load configuration
    config = get_config()
    
    print("📋 Configuration Values:")
    print(f"   TEAMS_WEBHOOK_URL: '{config.TEAMS_WEBHOOK_URL}'")
    print(f"   TEAMS_LOGIC_APP_URL: '{getattr(config, 'TEAMS_LOGIC_APP_URL', 'NOT_FOUND')}'")
    print(f"   Teams_LOGIC_APP_URL: '{getattr(config, 'Teams_LOGIC_APP_URL', 'NOT_FOUND')}'")
    print(f"   TEAMS_GRAPH_TOKEN: '{config.TEAMS_GRAPH_TOKEN}'")
    print(f"   Email_LOGIC_APP_URL: '{getattr(config, 'Email_LOGIC_APP_URL', 'NOT_FOUND')}'")
    print()
    
    # Test Teams client initialization
    print("🔌 Testing TeamsClient initialization:")
    teams_client = TeamsClient(config)
    
    print(f"   webhook_url: '{teams_client.webhook_url}'")
    print(f"   teams_logic_app_url: '{teams_client.teams_logic_app_url}'")
    print(f"   email_logic_app_url: '{teams_client.email_logic_app_url}'")
    print(f"   graph_token: '{teams_client.graph_token}'")
    print()
    
    # Test notification channel detection
    print("🔍 Notification Channel Detection:")
    if teams_client.teams_logic_app_url:
        print("   ✅ Teams Logic App URL configured")
    elif teams_client.webhook_url:
        print("   ✅ Teams Webhook URL configured")
    elif teams_client.email_logic_app_url:
        print("   ✅ Email Logic App URL configured (fallback)")
    else:
        print("   ❌ No notification channels configured")
    
    print()
    
    # Test config.get() method
    print("🧪 Testing config.get() method:")
    print(f"   config.get('TEAMS_WEBHOOK_URL'): '{config.get('TEAMS_WEBHOOK_URL')}'")
    print(f"   config.get('TEAMS_LOGIC_APP_URL'): '{config.get('TEAMS_LOGIC_APP_URL')}'")
    print(f"   config.get('Teams_LOGIC_APP_URL'): '{config.get('Teams_LOGIC_APP_URL')}'")
    print(f"   config.get('Email_LOGIC_APP_URL'): '{config.get('Email_LOGIC_APP_URL')}'")
    
    print()
    print("✨ Configuration test completed!")

if __name__ == "__main__":
    test_teams_config()
